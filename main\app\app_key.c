#include "app_key.h"
#include "key.h"
#include <stdio.h>

#define DRIVER_KEY_NUM_1 (0)
#define DRIVER_KEY_NUM_2 (8)

void Btn_0_handler(button *btn)
{
    switch (btn->btn_state) {
        case BTN_STA_IDLE:
            break;
        case BTN_STA_LONG_PRESS:
            printf("0 long press!\r\n");
            break;
        case BTN_STA_CLICK:
            printf("0 click!\r\n");
            break;
        case BTN_STA_DOUBLE_CLICK:
            printf("0 double click!\r\n");
            break;
    }
}

void Btn_1_handler(button *btn)
{
    switch (btn->btn_state) {
        case BTN_STA_IDLE:
            break;
        case BTN_STA_LONG_PRESS:
            printf("1 long press!\r\n");
            break;
        case BTN_STA_CLICK:
            printf("1 click!\r\n");
            break;
        case BTN_STA_DOUBLE_CLICK:
            printf("1 double click!\r\n");
            break;
    }
}

void app_key_init_1(void)
{
    register_button_handler(DRIVER_KEY_NUM_1, 0, &Btn_0_handler);
    register_button_handler(DRIVER_KEY_NUM_2, 1, &Btn_1_handler);
    app_key_init();
}
