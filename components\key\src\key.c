#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

#include "driver/gpio.h"

#include "key.h"

static const char *TAG = "APP_KEY";

static button btns[2] = {0};

// 键盘扫描函数
static void button_handler(button *btn)
{
    int gpio_level = gpio_get_level(btn->GPIO_Pin);

    if (btn->state > 0)
        btn->ticks++;

    if (btn->level != gpio_level)
    {
        if (++(btn->debounce_cnt) >= 3)
        {
            btn->level = gpio_level;
            btn->debounce_cnt = 0;
        }
    }
    else
    {
        btn->debounce_cnt = 0;
    }

    switch (btn->state)
    {
    case 0:
        if (btn->level == 0)
        {
            btn->state = 1;
            btn->ticks = 0;
            btn->repeat = 1;
        }
        else
        {
            btn->btn_state = BTN_STA_IDLE;
        }
        break;
    case 1:
        if (btn->level != 0)
        {
            btn->ticks = 0;
            btn->state = 2;
        }
        else if (btn->ticks >= 50)
        {
            btn->btn_state = BTN_STA_LONG_PRESS;
            if (btn->button_handler != NULL) {  // 添加空指针检查
                btn->button_handler(btn);
            }
            btn->ticks = 0;
            btn->repeat = 0;
        }
        break;
    case 2:
        if (btn->ticks > 15)
        {
            if (btn->repeat == 1)
            {
                btn->btn_state = BTN_STA_CLICK;
                if (btn->button_handler != NULL) {  // 添加空指针检查
                    btn->button_handler(btn);
                }
            }
            else if (btn->repeat == 2)
            {
                btn->btn_state = BTN_STA_DOUBLE_CLICK;
                if (btn->button_handler != NULL) {  // 添加空指针检查
                    btn->button_handler(btn);
                }
            }
            btn->state = 0;
        }
        else
        {
            if (btn->level == 0)
            {
                btn->repeat++;
                btn->state = 1;
                btn->ticks = 0;
            }
        }
        break;
    }
}

static void Key_Read_Task(void *arg)
{
    ESP_LOGI(TAG, "Key read task started");

    while (1)  // 简化为无限循环，这是嵌入式系统的常见模式
    {
        for (uint8_t i = 0; i < 2; i++)
        {
            // 只处理已注册的按键
            if (btns[i].button_handler != NULL) {
                button_handler(&btns[i]);
            }
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    // 注意：这行代码永远不会执行，但保留以防将来需要任务退出机制
    vTaskDelete(NULL);
}

void register_button_handler(uint8_t gpio_num, uint8_t id, button_callback_t handler)
{
    // 检查id是否在有效范围内
    if (id >= 2) {
        ESP_LOGE(TAG, "Button id %d out of range (max: 1)", id);
        return;
    }

    gpio_config_t io_conf = {};
    io_conf.pin_bit_mask = (1ULL << gpio_num);  // 修复：使用1ULL避免位移溢出
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 1;
    gpio_config(&io_conf);

    btns[id].GPIO_Pin = gpio_num;
    btns[id].level = 1;
    btns[id].id = id;
    btns[id].button_handler = handler;

    ESP_LOGI(TAG, "Registered button handler for GPIO%d, id=%d", gpio_num, id);
}

void app_key_init(void)
{
    ESP_LOGI(TAG, "Initializing custom key component");
    xTaskCreatePinnedToCore(&Key_Read_Task, "Key Read Task", 2 * 1024, NULL, 7, NULL, 1);
    ESP_LOGI(TAG, "Custom key component initialized");
}