#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#include "driver/gpio.h"

#include "key.h"

static const char *TAG = "APP_KEY";

static button btns[2] = {0};

// 键盘扫描函数
static void button_handler(button *btn)
{
    int gpio_level = gpio_get_level(btn->GPIO_Pin);

    if (btn->state > 0)
        btn->ticks++;

    if (btn->level != gpio_level)
    {
        if (++(btn->debounce_cnt) >= 3)
        {
            btn->level = gpio_level;
            btn->debounce_cnt = 0;
        }
    }
    else
    {
        btn->debounce_cnt = 0;
    }

    switch (btn->state)
    {
    case 0:
        if (btn->level == 0)
        {
            btn->state = 1;
            btn->ticks = 0;
            btn->repeat = 1;
        }
        else
        {
            btn->btn_state = BTN_STA_IDLE;
        }
        break;
    case 1:
        if (btn->level != 0)
        {
            btn->ticks = 0;
            btn->state = 2;
        }
        else if (btn->ticks >= 50)
        {
            btn->btn_state = BTN_STA_LONG_PRESS;
            btn->button_handler(btn);
            btn->ticks = 0;
            btn->repeat = 0;
        }
        break;
    case 2:
        if (btn->ticks > 15)
        {
            if (btn->repeat == 1)
            {
                btn->btn_state = BTN_STA_CLICK;
                btn->button_handler(btn);
            }
            else if (btn->repeat == 2)
            {
                btn->btn_state = BTN_STA_DOUBLE_CLICK;
                btn->button_handler(btn);
            }
            btn->state = 0;
        }
        else
        {
            if (btn->level == 0)
            {
                btn->repeat++;
                btn->state = 1;
                btn->ticks = 0;
            }
        }
        break;
    }
}

static void Key_Read_Task(void *arg)
{
    bool running = true;

    while (running)
    {
        for (uint8_t i = 0; i < 2; i++)
        {
            button_handler(&btns[i]);
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    vTaskDelete(NULL);
}

void register_button_handler(uint8_t gpio_num, uint8_t id, button_callback_t handler)
{
    gpio_config_t io_conf = {};
    io_conf.pin_bit_mask = (1 << gpio_num);
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 1;
    gpio_config(&io_conf);

    btns[id].GPIO_Pin = gpio_num;
    btns[id].level = 1;
    btns[id].id = id;
    btns[id].button_handler = handler;
}

void app_key_init(void)
{   
    xTaskCreatePinnedToCore(&Key_Read_Task, "Key Read Task", 2 * 1024, NULL, 7, NULL, 1);
}