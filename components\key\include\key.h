#pragma once

#include "hal/gpio_types.h"
#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    BTN_STA_IDLE,
    BTN_STA_LONG_PRESS,
    BTN_STA_CLICK,
    BTN_STA_DOUBLE_CLICK,
} button_state_t;

typedef struct button{
    uint16_t ticks; //按下的持续时间
    uint8_t repeat; //重复按下的次数
    int level; //读到的电平
    uint8_t debounce_cnt; //消抖次数
    uint8_t state; //按键状态
    uint8_t id; //按键编号
    button_state_t btn_state;
    
    gpio_num_t GPIO_Pin; //按键所属的引脚编号
    
    void (*button_handler)(struct button *btn);
}button;

typedef void (*button_callback_t)(button *btn);

void app_key_init();
void register_button_handler(uint8_t gpio_num, uint8_t id, button_callback_t handler);

#ifdef __cplusplus
}
#endif