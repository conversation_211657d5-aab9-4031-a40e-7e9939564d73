library,object,function,option
libesp_hw_support.a,rtc_module.c.obj,rtc_isr
libesp_hw_support.a,rtc_module.c.obj,rtc_isr_noniram_disable
libesp_hw_support.a,rtc_module.c.obj,rtc_isr_noniram_enable
libesp_hw_support.a,sleep_modes.c.obj,suspend_cache
libesp_hw_support.a,sleep_modes.c.obj,resume_cache
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_start
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_pu
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_finish
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_get_default_config
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_init
libesp_hw_support.a,rtc_sleep.c.obj,rtc_sleep_low_init
libesp_hw_support.a,rtc_time.c.obj,rtc_clk_cal
libesp_hw_support.a,rtc_time.c.obj,rtc_clk_cal_internal
libesp_hw_support.a,rtc_time.c.obj,rtc_time_get
libesp_hw_support.a,rtc_time.c.obj,rtc_time_us_to_slowclk
libesp_hw_support.a,rtc_time.c.obj,rtc_time_slowclk_to_us
libesp_hw_support.a,sleep_modes.c.obj,esp_sleep_enable_timer_wakeup,CONFIG_PM_SLP_IRAM_OPT
libesp_hw_support.a,periph_ctrl.c.obj,wifi_bt_common_module_enable,CONFIG_PM_ENABLE
libesp_hw_support.a,periph_ctrl.c.obj,wifi_bt_common_module_disable,CONFIG_PM_ENABLE
libesp_hw_support.a,periph_ctrl.c.obj,wifi_module_enable,CONFIG_PERIPH_CTRL_FUNC_IN_IRAM
libesp_hw_support.a,periph_ctrl.c.obj,wifi_module_disable,CONFIG_PERIPH_CTRL_FUNC_IN_IRAM
libesp_hw_support.a,esp_clk.c.obj,periph_module_reset,CONFIG_PERIPH_CTRL_FUNC_IN_IRAM
libesp_hw_support.a,esp_memory_utils.c.obj,esp_ptr_byte_accessible
libesp_hw_support.a,cpu.c.obj,esp_cpu_wait_for_intr
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_set_to_default_config
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_32k_enable_external
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_fast_src_set
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_freq_get_hz
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_src_get
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_slow_src_set
libesp_hw_support.a,rtc_clk.c.obj,rtc_dig_clk8m_disable
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_config_fast
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_8m_enable
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_8md256_enabled
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_configure
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_enable
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_get_config
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_mhz_to_config
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_config
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_8m
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_pll_mhz
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_set_xtal
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_xtal_freq_get
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_cpu_freq_to_xtal
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_bbpll_disable
libesp_hw_support.a,rtc_clk.c.obj,rtc_clk_apb_freq_update
libesp_hw_support.a,rtc_clk.c.obj,clk_ll_rtc_slow_get_src
libesp_hw_support.a,sleep_modes.c.obj,.iram1.*
libesp_system.a,cpu_start.c.obj,call_start_cpu0,FALSE
libesp_system.a,panic_handler.c.obj,xt_unhandled_exception
libesp_system.a,panic_handler.c.obj,panicHandler
libesp_system.a,crosscore_int.c.obj,esp_crosscore_isr,FALSE
libesp_system.a,crosscore_int.c.obj,esp_crosscore_int_send
libesp_system.a,crosscore_int.c.obj,esp_crosscore_int_send_yield
libesp_system.a,panic_handler.c.obj,panic_enable_cache
libesp_system.a,freertos_hooks.c.obj,esp_vApplicationIdleHook,CONFIG_PM_RTOS_IDLE_OPT
libesp_system.a,freertos_hooks.c.obj,esp_vApplicationTickHook
libesp_system.a,task_wdt.c.obj,idle_hook_cb,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt.c.obj,esp_task_wdt_reset,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt.c.obj,esp_task_wdt_reset_user,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt.c.obj,task_wdt_timer_feed,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt.c.obj,find_entry_and_check_all_reset,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt.c.obj,find_entry_from_task_handle_and_check_all_reset,CONFIG_PM_SLP_IRAM_OPT
libesp_system.a,task_wdt_impl_timergroup.c.obj,esp_task_wdt_impl_timer_feed,CONFIG_PM_SLP_IRAM_OPT
libesp_timer.a,esp_timer_impl_systimer.c.obj,timer_alarm_isr,FALSE
libesp_timer.a,esp_timer_impl_systimer.c.obj,esp_timer_get_time
libesp_timer.a,esp_timer.c.obj,esp_timer_get_next_alarm_for_wake_up,CONFIG_PM_ENABLE
libesp_timer.a,esp_timer.c.obj,timer_list_unlock
libesp_timer.a,esp_timer.c.obj,timer_list_lock
libfreertos.a,port_systick.c.obj,SysTickIsrHandler
libfreertos.a,port_systick.c.obj,xPortSysTickHandler
libfreertos.a,portasm.S.obj,rtos_int_enter
libfreertos.a,port.c.obj,vPortEnterCritical
libfreertos.a,port.c.obj,vPortExitCritical
libfreertos.a,port.c.obj,vPortYieldFromISR
libfreertos.a,port.c.obj,vPortSetInterruptMask
libfreertos.a,port.c.obj,xPortInIsrContext
libfreertos.a,port.c.obj,vPortYield
libfreertos.a,port.c.obj,vPortClearInterruptMask
libfreertos.a,port.c.obj,xPortSetInterruptMaskFromISR
libfreertos.a,port.c.obj,vPortClearInterruptMaskFromISR
libfreertos.a,tasks.c.obj,xTaskGetCurrentTaskHandle
libfreertos.a,tasks.c.obj,xTaskGetSchedulerState
libfreertos.a,tasks.c.obj,xTaskIncrementTick
libfreertos.a,tasks.c.obj,prvResetNextTaskUnblockTime
libfreertos.a,tasks.c.obj,vTaskStepTick,CONFIG_FREERTOS_USE_TICKLESS_IDLE
libfreertos.a,port_common.c.obj,xPortCheckValidTCBMem
libfreertos.a,queue.c.obj,xQueueSemaphoreTake
libfreertos.a,queue.c.obj,xQueueGenericSend
libfreertos.a,queue.c.obj,xQueueReceive
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_program_page
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_erase_sector
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_erase_block
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_erase_block
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_read
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_common_command
libhal.a,spi_flash_hal_iram.c.obj,spi_flash_hal_resume
libhal.a,spi_flash_hal_gpspi.c.obj,spi_flash_hal_gpspi_poll_cmd_done
libhal.a,systimer_hal.c.obj,.text.*
libhal.a,mmu_hal.c.obj,.iram1.*
libhal.a,mmu_hal.c.obj,.text.*
libfreertos.a,portasm.S.obj,.text.*
libfreertos.a,list.c.obj,.text.*,FALSE
libfreertos.a,queue.c.obj,.text.*,FALSE
libfreertos.a,queue.c.obj,.iram1.*,FALSE
libhal.a,cache_hal.c.obj,.iram1.*
libhal.a,cache_hal.c.obj,.text.*
libspi_flash.a,cache_utils.c.obj,spi_flash_cache_enabled
libspi_flash.a,cache_utils.c.obj,spi_flash_enable_cache
libspi_flash.a,cache_utils.c.obj,spi_flash_restore_cache
libspi_flash.a,cache_utils.c.obj,spi_flash_disable_cache
libspi_flash.a,cache_utils.c.obj,spi_flash_disable_interrupts_caches_and_other_cpu
libspi_flash.a,cache_utils.c.obj,spi_flash_enable_interrupts_caches_and_other_cpu
libspi_flash.a,flash_mmap.c.obj,spi_flash_mmap_pages
libspi_flash.a,flash_mmap.c.obj,spi_flash_mmap_init
libspi_flash.a,flash_mmap.c.obj,get_mmu_region
libspi_flash.a,flash_mmap.c.obj,spi_flash_munmap
libspi_flash.a,flash_mmap.c.obj,spi_flash_protected_read_mmu_entry
libspi_flash.a,flash_mmap.c.obj,spi_flash_mmap_get_free_pages
libesp_pm.a,pm_impl.c.obj,esp_pm_impl_isr_hook,CONFIG_PM_ENABLE
libesp_pm.a,pm_impl.c.obj,vApplicationSleep,CONFIG_PM_ENABLE
libesp_pm.a,pm_impl.c.obj,esp_pm_impl_idle_hook,CONFIG_PM_RTOS_IDLE_OPT
libesp_pm.a,pm_impl.c.obj,esp_pm_impl_waiti,CONFIG_PM_RTOS_IDLE_OPT
libesp_pm.a,pm_locks.c.obj,esp_pm_lock_acquire,CONFIG_PM_ENABLE
libesp_pm.a,pm_locks.c.obj,esp_pm_lock_release,CONFIG_PM_ENABLE
libesp_phy.a,phy_init.c.obj,esp_phy_disable,CONFIG_PM_ENABLE
libesp_phy.a,phy_init.c.obj,esp_phy_enable,CONFIG_PM_ENABLE
libesp_phy.a,phy_init.c.obj,esp_phy_common_clock_disable,CONFIG_PM_ENABLE
libesp_phy.a,phy_init.c.obj,esp_phy_common_clock_enable,CONFIG_PM_ENABLE
libnewlib.a,locks.c.obj,_lock_acquire,CONFIG_PM_ENABLE
libnewlib.a,locks.c.obj,_lock_release,CONFIG_PM_ENABLE
libnewlib.a,locks.c.obj,.text.*
libnewlib.a,locks.c.obj,.iram1.*
libnewlib.a,assert.c.obj,__assert_func
libesp_wifi.a,esp_adapter.c.obj,wifi_apb80m_release_wrapper,CONFIG_PM_ENABLE
libesp_wifi.a,esp_adapter.c.obj,wifi_apb80m_request_wrapper,CONFIG_PM_ENABLE
libesp_wifi.a,esp_adapter.c.obj,wifi_reset_mac_wrapper,CONFIG_PM_ENABLE
libesp_wifi.a,esp_adapter.c.obj,wifi_clock_disable_wrapper,CONFIG_ESP_WIFI_SLP_IRAM_OPT
libesp_wifi.a,esp_adapter.c.obj,wifi_clock_enable_wrapper,CONFIG_ESP_WIFI_SLP_IRAM_OPT
libesp_wifi.a,wifi_init.c.obj,wifi_apb80m_release,CONFIG_PM_ENABLE
libesp_wifi.a,wifi_init.c.obj,wifi_apb80m_request,CONFIG_PM_ENABLE
libesp_netif.a,ethernetif.c.obj,ethernet_low_level_output,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,ethernetif.c.obj,ethernetif_input,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,wlanif.c.obj,low_level_output,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,wlanif.c.obj,wlanif_input,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,esp_netif_lwip.c.obj,esp_netif_transmit_wrap,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,esp_netif_lwip.c.obj,esp_netif_free_rx_buffer,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,esp_netif_lwip.c.obj,esp_netif_receive,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,esp_pbuf_ref.c.obj,esp_pbuf_allocate,CONFIG_LWIP_IRAM_OPTIMIZATION
libesp_netif.a,esp_pbuf_ref.c.obj,esp_pbuf_free,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,api_msg.c.obj,lwip_netconn_do_send,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,api_msg.c.obj,lwip_netconn_do_write,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,netbuf.c.obj,netbuf_alloc,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,netbuf.c.obj,netbuf_free,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_thread,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_thread_handle_msg,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_inpkt,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_input,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_callback,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_try_callback,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,tcpip.c.obj,tcpip_send_msg_wait_sem,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,inet_chksum.c.obj,inet_cksum_pseudo_base,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,inet_chksum.c.obj,inet_chksum_pseudo,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,etharp.c.obj,etharp_output_to_arp_index,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,etharp.c.obj,etharp_output,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4_addr.c.obj,ip4_addr_isbroadcast_u32,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_route_src,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_route,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_input,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_output_if,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_output_if_opt,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_output_if_src,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_output_if_opt_src,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ip4.c.obj,ip4_output,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_alloc,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_add_header_impl,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_add_header,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_remove_header,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_header_impl,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_header,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_free,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_alloced_custom,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,pbuf.c.obj,pbuf_init_alloced_pbuf,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_input_local_match,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_input,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_send,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_sendto,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_sendto_if,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,udp.c.obj,udp_sendto_if_src,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ethernet.c.obj,ethernet_input,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,ethernet.c.obj,ethernet_output,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_mutex_lock,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_mutex_unlock,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_sem_signal,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_arch_sem_wait,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_mbox_post,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_mbox_trypost,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,sys_arch.c.obj,sys_arch_mbox_fetch,CONFIG_LWIP_IRAM_OPTIMIZATION
liblwip.a,lwip_default_hooks.c.obj,ip4_route_src_hook,CONFIG_LWIP_IRAM_OPTIMIZATION
libriscv.a,interrupt.c.obj,.text.*
libriscv.a,vectors.S.obj,.text.*
libfreertos.a,portasm.S.obj,.text.*
libfreertos.a,list.c.obj,.text.*
libpp.a,*,.iram1.*
libpp.a,*,.wifi0iram.*,CONFIG_ESP32_WIFI_IRAM_OPT
libpp.a,*,.wifirxiram.*,CONFIG_ESP32_WIFI_IRAM_OPT
libnet80211.a,*,.wifi0iram.*,CONFIG_ESP32_WIFI_IRAM_OPT
libnet80211.a,*,.wifirxiram.*,CONFIG_ESP32_WIFI_IRAM_OPT
libesp_mm.a,esp_mmu_map.c.obj,.iram1.*
libesp_mm.a,esp_mmu_map.c.obj,.text.*,FALSE
libesp_mm.a,esp_mmu_map.c.obj,s_do_cache_invalidate,
libesp_mm.a,esp_mmu_map.c.obj,s_do_mapping,
libesp_mm.a,esp_mmu_map.c.obj,s_do_unmapping,
libesp_mm.a,esp_mmu_map.c.obj,esp_mmu_vaddr_to_paddr,
libhal.a,mmu_hal.c.obj,.iram1.*
libhal.a,mmu_hal.c.obj,.text.*
